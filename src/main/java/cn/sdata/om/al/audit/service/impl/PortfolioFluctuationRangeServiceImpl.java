package cn.sdata.om.al.audit.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.sdata.om.al.audit.dto.PortfolioFluctuationRangeDTO;
import cn.sdata.om.al.audit.entity.PortfolioFluctuationRange;
import cn.sdata.om.al.audit.mapper.PortfolioFluctuationRangeMapper;
import cn.sdata.om.al.audit.service.PortfolioFluctuationRangeService;
import cn.sdata.om.al.audit.util.CompareUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 组合净值波动范围规则Service实现
 */
@Service
public class PortfolioFluctuationRangeServiceImpl extends ServiceImpl<PortfolioFluctuationRangeMapper, PortfolioFluctuationRange> implements PortfolioFluctuationRangeService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveFluctuationRange(PortfolioFluctuationRangeDTO rangeDTO) {
        // 生成规则标识
        String ruleKey = IdUtil.fastSimpleUUID();

        // 将DTO转换为实体类列表
        List<PortfolioFluctuationRange> rangeList = convertDtoToEntities(rangeDTO, ruleKey);

        // 批量保存
        return this.saveBatch(rangeList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFluctuationRange(PortfolioFluctuationRangeDTO rangeDTO) {
        String ruleKey = rangeDTO.getRuleKey();
        deleteFluctuationRange(ruleKey);
        return saveFluctuationRange(rangeDTO);
    }

    @Override
    public boolean deleteFluctuationRange(String ruleKey) {
        LambdaQueryWrapper<PortfolioFluctuationRange> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PortfolioFluctuationRange::getRuleKey, ruleKey);
        return this.baseMapper.delete(queryWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveFluctuationRanges(List<PortfolioFluctuationRangeDTO> rangeDTOList) {
        // 先清除所有旧数据
        this.remove(new LambdaQueryWrapper<>());

        // 处理所有规则DTO
        List<PortfolioFluctuationRange> allRanges = new ArrayList<>();
        for (PortfolioFluctuationRangeDTO rangeDTO : rangeDTOList) {
            // 生成规则标识
            String ruleKey = IdUtil.fastSimpleUUID();
            // 转换为实体类列表
            List<PortfolioFluctuationRange> rangeList = convertDtoToEntities(rangeDTO, ruleKey);
            allRanges.addAll(rangeList);
        }

        // 批量保存
        return this.saveBatch(allRanges);
    }

    @Override
    public List<PortfolioFluctuationRangeDTO> listAllRanges() {
        // 查询所有规则数据
        List<PortfolioFluctuationRange> allRanges = this.list();

        // 按规则标识分组
        Map<String, List<PortfolioFluctuationRange>> groupedRanges = allRanges.stream()
                .collect(Collectors.groupingBy(PortfolioFluctuationRange::getRuleKey));

        // 转换为DTO列表
        List<PortfolioFluctuationRangeDTO> result = new ArrayList<>();
        for (Map.Entry<String, List<PortfolioFluctuationRange>> entry : groupedRanges.entrySet()) {
            List<PortfolioFluctuationRange> rangeList = entry.getValue();

            // 确保有数据
            if (rangeList.isEmpty()) {
                continue;
            }

            // 创建DTO
            PortfolioFluctuationRangeDTO dto = new PortfolioFluctuationRangeDTO();

            // 设置公共属性（从第一条记录中获取）
            PortfolioFluctuationRange firstRange = rangeList.get(0);
            dto.setLowerCompareType(firstRange.getLowerCompareType());
            dto.setUpperCompareType(firstRange.getUpperCompareType());
            dto.setLowerLimit(firstRange.getLowerLimit());
            dto.setUpperLimit(firstRange.getUpperLimit());
            dto.setRuleKey(firstRange.getRuleKey());
            dto.setCreateTime(firstRange.getCreateTime());
            dto.setUpdateTime(firstRange.getUpdateTime());

            // 设置产品/账套列表
            List<PortfolioFluctuationRangeDTO.Portfolio> portfolios = new ArrayList<>();
            for (PortfolioFluctuationRange range : rangeList) {
                PortfolioFluctuationRangeDTO.Portfolio portfolio = new PortfolioFluctuationRangeDTO.Portfolio();
                portfolio.setProductId(range.getProductId());
                portfolio.setProductName(range.getProductName());
                portfolios.add(portfolio);
            }
            dto.setPortfolios(portfolios);

            result.add(dto);
        }
        if (CollectionUtil.isNotEmpty(result)) {
            result = result.stream().sorted(Comparator.comparing(PortfolioFluctuationRangeDTO::getUpdateTime)).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public boolean validateFluctuationRate(BigDecimal fluctuationRate, String productId) {
        // 如果净值波动率为空或产品ID为空，默认通过（不异常）
        if (fluctuationRate == null || productId == null) {
            return true;
        }

        // 查询该产品的波动范围规则
        LambdaQueryWrapper<PortfolioFluctuationRange> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PortfolioFluctuationRange::getProductId, productId);
        List<PortfolioFluctuationRange> ranges = this.list(queryWrapper);

        // 如果没有规则，默认通过
        if (ranges.isEmpty()) {
            return true;
        }

        // 检查是否在任一规则范围内
        for (PortfolioFluctuationRange range : ranges) {
            boolean inRange = CompareUtil.isInRange(
                    fluctuationRate,
                    range.getLowerLimit(),
                    range.getLowerCompareType(),
                    range.getUpperLimit(),
                    range.getUpperCompareType()
            );

            if (inRange) {
                return true;
            }
        }

        return false;
    }

    @Override
    public boolean validateFluctuationRateByRangeList(BigDecimal fluctuationRate, String productId, List<PortfolioFluctuationRange> ranges) {
        // 如果净值波动率为空或产品ID为空，默认通过（不异常）
        if (fluctuationRate == null || productId == null) {
            return true;
        }

        // 如果没有规则，默认通过
        if (CollectionUtil.isEmpty(ranges)) {
            return true;
        }
        ranges = ranges.stream().filter(n -> productId.equals(n.getProductId())).collect(Collectors.toList());

        // 如果该产品没有对应的规则，默认通过
        if (CollectionUtil.isEmpty(ranges)) {
            return true;
        }

        // 检查是否在任一规则范围内
        for (PortfolioFluctuationRange range : ranges) {
            boolean inRange = CompareUtil.isInRange(
                    fluctuationRate,
                    range.getLowerLimit(),
                    range.getLowerCompareType(),
                    range.getUpperLimit(),
                    range.getUpperCompareType()
            );

            if (inRange) {
                return true;
            }
        }

        return false;
    }

    /**
     * 将DTO转换为实体类列表
     */
    private List<PortfolioFluctuationRange> convertDtoToEntities(PortfolioFluctuationRangeDTO dto, String ruleKey) {
        List<PortfolioFluctuationRange> result = new ArrayList<>();
        Date now = new Date();

        // 遍历产品/账套列表，为每个产品/账套创建一条记录
        if (dto.getPortfolios() != null && !dto.getPortfolios().isEmpty()) {
            for (PortfolioFluctuationRangeDTO.Portfolio portfolio : dto.getPortfolios()) {
                PortfolioFluctuationRange range = new PortfolioFluctuationRange();
                range.setRuleKey(ruleKey);
                range.setProductId(portfolio.getProductId());
                range.setProductName(portfolio.getProductName());
                range.setLowerCompareType(dto.getLowerCompareType());
                range.setUpperCompareType(dto.getUpperCompareType());
                range.setLowerLimit(dto.getLowerLimit());
                range.setUpperLimit(dto.getUpperLimit());
                range.setCreateTime(now);
                range.setUpdateTime(now);

                result.add(range);
            }
        }

        return result;
    }
}
