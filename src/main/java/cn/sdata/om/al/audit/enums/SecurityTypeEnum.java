package cn.sdata.om.al.audit.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SecurityTypeEnum {
    // 枚举定义：name(展示名, l_zqlb, code)
    DEBT_INVEST_PLAN("债权投资计划", 2, "15"),
    EQUITY_INVEST_PLAN("股权投资计划", 4, "16"),
    TRUST_PLAN("信托计划", 4, "6"),
    ABS_PLAN("资产支持计划", 4, "11");

    private final String name;
    // 大类标识 l_zqlb
    private final Integer lzqlb;
    // 小类编码 L_ZQLBMX1
    private final String code;

    // 根据 name 获取枚举
    public static SecurityTypeEnum fromName(String name) {
        for (SecurityTypeEnum type : values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    // 根据 code 获取枚举
    public static SecurityTypeEnum fromCode(String code) {
        for (SecurityTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
