package cn.sdata.om.al.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.constant.RPAConstant;
import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.entity.FlowList;
import cn.sdata.om.al.entity.RpaExecLog;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.mapper.AccountInformationMapper;
import cn.sdata.om.al.mapper.BaseCronLogMapper;
import cn.sdata.om.al.mapper.FlowListMapper;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.annotation.TradeDay;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.enums.JobStatus;
import cn.sdata.om.al.qrtz.service.BaseCronLogService;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.RpaExecuteService;
import cn.sdata.om.al.utils.LogBRUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.BaseConstant.*;
import static cn.sdata.om.al.constant.RPAConstant.LOG_ID;

@Slf4j
@Component
@RequiredArgsConstructor
@TradeDay
public class BankReconciliationZZRpaJob implements QuartzJobBean {

    private CronService cronService;

    private RpaExecuteService rpaExecuteService;

    private BaseCronLogService baseCronLogService;

    @Autowired
    public void setCronService(CronService cronService) {
        this.cronService = cronService;
    }

    @Autowired
    public void setRpaExecuteService(RpaExecuteService rpaExecuteService) {
        this.rpaExecuteService = rpaExecuteService;
    }

    @Autowired
    public void setBaseCronLogService(BaseCronLogService baseCronLogService) {
        this.baseCronLogService = baseCronLogService;
    }

    @Override
    public void doExecute(JobExecutionContext context) {

    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("---------开始调度中债银行对账自动任务");
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String dataDate = mergedJobDataMap.getString("dataDate");
        String accountNumber = mergedJobDataMap.getString("accountNumber");
        if (StringUtils.isBlank(dataDate)) {
            dataDate = DateUtil.today();
        }
        if (StringUtils.isBlank(accountNumber)) {
            List<AccountInformation> accountInformations = SpringUtil.getBean(AccountInformationMapper.class).selectList(null);
            if (CollectionUtil.isNotEmpty(accountInformations)) {
                accountNumber = accountInformations.stream()
                        .map(AccountInformation::getCentralDebtAccountNumber)
                        .filter(StringUtils::isNotBlank)
                        .map(String::trim)
                        .filter(s -> !"-".equals(s))
                        .distinct()
                        .collect(Collectors.joining(","));
                log.info("需要下载的中债持有人账号为:{}", accountNumber);
            }
        }
        if (StringUtils.isBlank(accountNumber)) {
            throw new RuntimeException("中债持有人账号为null");
        }
        BaseCronLog baseCronLog = new BaseCronLog();
        BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
        Objects.requireNonNull(context, "BankReconciliationZZRpaJob JobExecutionContext is null");
        String jobId = (String) mergedJobDataMap.get(CronConstant.JOB_ID);
        String logId = IdUtil.getSnowflakeNextIdStr();
        baseCronLog.setId(logId);
        baseCronLog.setStartDateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        baseCronLog.setStatus(JobStatus.RUNNING);
        baseCronLog.setTaskId(jobId);
        String username = mergedJobDataMap.getString("username");
        baseCronLog.setExecutor(StringUtils.isNotBlank(username) ? username : DEFAULT_USERNAME);
        String type = mergedJobDataMap.getString("type");
        baseCronLog.setExecuteMethod(StringUtils.isNotBlank(type) ? type : "AUTO");
        baseCronLog.setDataDate(DateUtil.format(DateUtil.parseDate(dataDate), "yyyy年MM月dd日") + "-" + DateUtil.format(DateUtil.parseDate(dataDate), "yyyy年MM月dd日"));
        baseCronLogMapper.insert(baseCronLog);
        try {
            Cron cronInfo = cronService.getById(jobId);
            Integer flowId = cronInfo.getFlowId();
            FlowList flow = SpringUtil.getBean(FlowListMapper.class).selectById(flowId);
            if (flow == null) {
                log.error("BankReconciliationZZRpaJob_doExecute_error:flowList对象为空");
                BusinessException.throwException("flowList对象为空");
            }
            if (StringUtils.isBlank(dataDate)) {
                log.error("BankReconciliationZZRpaJob_doExecute_error:dataDate为空");
                BusinessException.throwException("dataDate为空");
            }
            Map<String, Object> flowExtendParams = new HashMap<>();
            flowExtendParams.put(RPA_START_DATE_NAME, dataDate);
            flowExtendParams.put(ZZ_BR_ACCOUNT_NUMBER, accountNumber);
            JSONObject params = new JSONObject();
            params.put("rpaLogId", logId);
            params.put("username", DEFAULT_USERNAME);
            params.put("logId", IdUtil.getSnowflakeNextIdStr());
            params.put("params", JSONObject.toJSONString(mergedJobDataMap));
            params.put("dataDate", dataDate);
            params.put("type", "AUTO");
            JSONObject rpaParams = new JSONObject();
            rpaParams.put("flow", flow);
            rpaParams.put("flowExtendParams", flowExtendParams);
            params.put("rpaParam", JSON.toJSONString(rpaParams));
            LogBRUtil.preRpaLog(params);
            RpaExecLog rpaExecLog = rpaExecuteService.executeRPA(flow, flowExtendParams, logId);
            Map<String, Object> param = new HashMap<>();
            param.put(RPAConstant.CRON, cronInfo);
            param.put(LOG_ID, logId);
            param.put(SYSTEM_DATE_NAME, dataDate);
            param.put(CronConstant.EXECUTOR, DEFAULT_USERNAME);
            rpaExecuteService.startTimer(rpaExecLog, flow, param, logId);
            log.info("中债银行对账单-手动下载-发起RPA调用结束");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("BankReconciliationZZRpaJob_doExecute_error:{},{}", e, e.getMessage());
            LambdaUpdateWrapper<BaseCronLog> uw = new LambdaUpdateWrapper<>();
            uw.eq(BaseCronLog::getId, logId);
            uw.set(BaseCronLog::getStatus, JobStatus.FAILED).set(BaseCronLog::getEndDateTime, DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            baseCronLogService.update(uw);
            BusinessException.throwException(e);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("BankReconciliationZZRpaJob", BankReconciliationZZRpaJob.class);
    }
}
