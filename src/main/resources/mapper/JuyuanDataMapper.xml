<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.audit.mapper.JuyuanDataMapper">

    <!-- 股票分红公告信息查询 -->
    <select id="queryStockDividendData" resultType="cn.sdata.om.al.audit.entity.DividendAnnouncementData">
        SELECT DISTINCT ab.id as juyuanId,
               ab.INNERCODE as innerCode,
               c.SECUCODE as securityCode,
               c.SECUMARKET as securityMarket,
               c.SECUCATEGORYONE as securityType,
               c.SECUABBR as securityName,
               'STOCK' as dataSourceType,
               ab.ENDDATE,
               ab.RIGHTREGDATE as registrationDate,
               ab.EXDIVIDATE as exRightsDate,
               ab.TOACCOUNTDATE as bonusSharePaymentDate,
               ab.BONUSSHAREARRIVALDATE as dividendPaymentDate,
               ab.CASHDIVIRMBADJUSTED as cashDividendRatio,
               ab.BONUSSHARERATIO as bonusShareRatio
        FROM (SELECT DISTINCT a.*
              FROM LC_DIVIDEND a
                       INNER JOIN (SELECT max(id) AS id,
                                          INNERCODE
                                   FROM LC_DIVIDEND WHERE EXDIVIDATE is not null
                                   GROUP BY INNERCODE) b ON
                  a.id = b.id) ab
                 INNER JOIN SECUMAINALL c ON
            ab.INNERCODE = c.INNERCODE
        ORDER BY ab.id DESC
    </select>

    <!-- 港股分红公告信息查询 -->
    <select id="queryHkStockDividendData" resultType="cn.sdata.om.al.audit.entity.DividendAnnouncementData">
        SELECT DISTINCT ab.id as juyuanId,
               ab.INNERCODE as innerCode,
               CONCAT('H', c.SECUCODE) as securityCode,
               c.SECUMARKET as securityMarket,
               c.SECUCATEGORY as securityType,
               c.SECUABBR as securityName,
               'HKSTOCK' as dataSourceType,
               ab.ENDDATE,
               ab.RECORDDATE as registrationDate,
               ab.EXDATE as exRightsDate,
               ab.DIVIDENDPAYABLEDATE as dividendPaymentDate,
               ab.PAYOUTDATE as bonusSharePaymentDate,
               ab.CASHDIVIDENDPS*10 as cashDividendRatio,
               ab.ShareDividendRateX*10 / ab.ShareDividendRateY as bonusShareRatio
        FROM (SELECT DISTINCT a.*
              FROM HK_DIVIDEND a
                       INNER JOIN (SELECT max(id) AS id,
                                          INNERCODE
                                   FROM HK_DIVIDEND WHERE EXDATE is not null
                                   GROUP BY INNERCODE) b ON
                  a.id = b.id) ab
                 INNER JOIN (SELECT DISTINCT INNERCODE, SECUCODE, SECUMARKET, SECUCATEGORY, SECUABBR FROM HK_SECUMAIN) c ON
            ab.INNERCODE = c.INNERCODE
        ORDER BY ab.id DESC
    </select>

    <!-- 债券分红公告信息查询 -->
    <select id="queryBondDividendData" resultType="cn.sdata.om.al.audit.entity.DividendAnnouncementData">
        SELECT DISTINCT ab.id as juyuanId,
               ab.INNERCODE as innerCode,
               c.SECUCODE as securityCode,
               c.SECUMARKET as securityMarket,
               c.SECUCATEGORYONE as securityType,
               c.SECUABBR as securityName,
               'BOND' as dataSourceType,
               ab.REGDATE as registrationDate,
               ab.EXDIVIDATE as exRightsDate,
               ab.PAYMENTDATE as dividendPaymentDate,
               ab.INTERESTPER as cashDividendRatio
        FROM (SELECT DISTINCT a.*
              FROM BOND_CASHFLOW a
                       INNER JOIN (SELECT max(id) AS id,
                                          INNERCODE
                                   FROM BOND_CASHFLOW
                                   WHERE INSERTTIME BETWEEN to_date(#{startDate}, 'YYYY-MM-DD') AND to_date(#{endDate}, 'YYYY-MM-DD')
                                    AND EXDIVIDATE is not null
                                   GROUP BY INNERCODE) b ON
                  a.id = b.id
             ) ab
                 INNER JOIN SECUMAINALL c ON
            ab.INNERCODE = c.INNERCODE
        ORDER BY ab.id DESC
    </select>

    <!-- 基金分红公告信息查询 -->
    <select id="queryFundDividendData" resultType="cn.sdata.om.al.audit.entity.DividendAnnouncementData">
        SELECT DISTINCT ab.id as juyuanId,
               ab.INNERCODE as innerCode,
               c.SECUCODE as securityCode,
               c.SECUMARKET as securityMarket,
               c.SECUCATEGORYONE as securityType,
               c.SECUABBR as securityName,
               'FUND' as dataSourceType,
               ab.ENDDATE,
               ab.REDATE as registrationDate,
               ab.EXRIGHTDATE as exRightsDate,
               ab.EXECUTEDATE as dividendPaymentDate,
               ab.ACCOUNTDAY as bonusSharePaymentDate,
               ab.DIVIDENDRATIOBEFORETAX as cashDividendRatio
        FROM (SELECT DISTINCT a.*
              FROM MF_DIVIDEND a
                       INNER JOIN (SELECT max(id) AS id,
                                          INNERCODE
                                   FROM MF_DIVIDEND WHERE EXRIGHTDATE is not null
                                   GROUP BY INNERCODE) b ON
                  a.id = b.id) ab
                 INNER JOIN SECUMAINALL c ON
            ab.INNERCODE = c.INNERCODE
        ORDER BY ab.id DESC
    </select>

    <!-- 股票指标分红公告信息查询 -->
    <select id="queryStockIndicatorDividendData" resultType="cn.sdata.om.al.audit.entity.DividendAnnouncementData">
        SELECT DISTINCT ab.id as juyuanId,
               ab.INNERCODE as innerCode,
               c.SECUCODE as securityCode,
               c.SECUMARKET as securityMarket,
               c.SECUCATEGORYONE as securityType,
               c.SECUABBR as securityName,
               'STOCK' as dataSourceType,
               ab.ENDDATE,
               ab.RIGHTREGDATE as registrationDate,
               ab.EXDIVIDATE as exRightsDate,
               ab.TOACCOUNTDATE as bonusSharePaymentDate,
               ab.BONUSARRIVALDATE as dividendPaymentDate,
               ab.CASHDIVIRMBADJUSTED as cashDividendRatio,
               ab.BONUSSHARERATIO as bonusShareRatio
        FROM (SELECT DISTINCT a.*
              FROM LC_STIBDIVIDEND a
                       INNER JOIN (SELECT max(id) AS id,
                                          INNERCODE
                                   FROM LC_STIBDIVIDEND  WHERE EXDIVIDATE is not null
                                   GROUP BY INNERCODE) b ON
                  a.id = b.id) ab
                 INNER JOIN SECUMAINALL c ON
            ab.INNERCODE = c.INNERCODE
        ORDER BY ab.id DESC
    </select>

    <!-- 查询所有债券分红公告信息（不限制日期） -->
    <select id="queryAllBondDividendData" resultType="cn.sdata.om.al.audit.entity.DividendAnnouncementData">
        SELECT DISTINCT ab.id as juyuanId,
               ab.INNERCODE as innerCode,
               c.SECUCODE as securityCode,
               c.SECUMARKET as securityMarket,
               c.SECUCATEGORYONE as securityType,
               c.SECUABBR as securityName,
               'BOND' as dataSourceType,
               ab.REGDATE as registrationDate,
               ab.EXDIVIDATE as exRightsDate,
               ab.PAYMENTDATE as dividendPaymentDate,
               ab.INTERESTPER as cashDividendRatio
        FROM (SELECT DISTINCT a.*
              FROM BOND_CASHFLOW a
                       INNER JOIN (SELECT max(id) AS id,
                                          INNERCODE
                                   FROM BOND_CASHFLOW WHERE EXDIVIDATE is not null
                                   GROUP BY INNERCODE) b ON
                  a.id = b.id
             ) ab
                 INNER JOIN SECUMAINALL c ON
            ab.INNERCODE = c.INNERCODE
        ORDER BY ab.id DESC
    </select>

</mapper>
