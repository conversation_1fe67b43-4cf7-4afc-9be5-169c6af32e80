spring:
  application:
    name: om_backend_al
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: *************************************
          username: root
          password: sData#888
          driver-class-name: com.mysql.cj.jdbc.Driver
          hikari:
            # 设置用于测试连接的SQL查询语句
            connection-test-query: SELECT 1
            # 设置获取数据库连接的超时时间，默认为30秒，单位是秒
            connection-timeout: 30000
            # 设置连接在连接池中保持空闲的最长时间，默认为10分钟，单位是秒
            idle-timeout: 30000
            # 设置连接在连接池中允许存在的最长时间，默认为30分钟，单位是秒
            max-lifetime: 1800000
            # 设置连接池中允许的最大连接数，默认为10
            maximum-pool-size: 30
            # 设置连接池中保持的最小空闲连接数，默认为10
            minimum-idle: 5
            # 设置连接验证的超时时间，默认为5秒，单位是秒
            validation-timeout: 300
        valuation:
          url: ********************************************
          username: HSFA
          password: HSFA
          driver-class-name: oracle.jdbc.driver.OracleDriver
        ta:
          url: ********************************************
          username: FUND60QUERY
          password: FUND60QUERY
          driver-class-name: oracle.jdbc.driver.OracleDriver
        cop:
          url: *********************************
          username: root
          password: Aa3edc#EDC
          driver-class-name: com.mysql.cj.jdbc.Driver
        o32:
          url: ******************************************
          username: trade
          password: trade
          driver-class-name: oracle.jdbc.driver.OracleDriver
        juyuan:
          url: ********************************************
          username: JYDB
          password: JYDB
          driver-class-name: oracle.jdbc.driver.OracleDriver
      lazy: true
  servlet:
    multipart:
      max-file-size: 10MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  quartz:
    # 任务存储类型
    job-store-type: jdbc
    # 关闭时等待任务完成
    wait-for-jobs-to-complete-on-shutdown: false
    # 是否覆盖已有的任务
    overwrite-existing-jobs: true
    # 是否自动启动计划程序
    auto-startup: true
    # 延迟启动
    startup-delay: 0s
    jdbc:
      # 数据库架构初始化模式（never：从不进行初始化；always：每次都清空数据库进行初始化；embedded：只初始化内存数据库（默认值））
      # 注意：第一次启动后，需要将always改为never，否则后续每次启动都会重新初始化quartz数据库
      initialize-schema: never
      # 用于初始化数据库架构的SQL文件的路径
      schema: classpath:sql/quartz.sql
    # 相关属性配置
    properties:
      org:
        quartz:
          scheduler:
            # 调度器实例名称
            instanceName: QuartzScheduler
            # 分布式节点ID自动生成
            instanceId: AUTO
          jobStore:
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            # 表前缀
            tablePrefix: qrtz_
            # 是否开启集群
            isClustered: true
            # 数据源别名（自定义）
            dataSource: quartz
            # 分布式节点有效性检查时间间隔（毫秒）
            clusterCheckinInterval: 10000
            useProperties: false
          # 线程池配置
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
server:
  port: 9999
smb:
  host: ************
  username: yangjiawei
  password: sData#789
  domain: ''
  base: s-data
  upload: 常用软件\upload

pagehelper:
  reasonable: true
  helper-dialect: mysql

mybatis-plus:
  configuration:
    cache-enabled: false
    local-cache-scope: statement
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath:mapper/*.xml

rpa:
  interNetAddress: http://localhost:8080/CallFunc.aom
  interNetAppName: POC测试
  interNetAppPass: D488A1F07A474051B7ECD4EA60B61038
  outerNetAddress: http://localhost:8080/CallFunc.aom
  outerNetAppName: POC测试
  outerNetAppPass: D488A1F07A474051B7ECD4EA60B61038
  modelKey: "{9F8E5ECB-5976-4315-B8F3-43B8502B694D}"
  methodKey: "{2881E26D-62CE-4937-B4BB-8998440417C4}"
  errorMsgKey: "{********-8A69-4A6B-A8B5-61F882EDE4F3}"

email:
  host: smtp.qq.com
  name: <EMAIL>
  sender: <EMAIL>
  password: omecnbxvdbvxbdcb
  defaultEncoding: utf-8
  port: 587
  protocol: smtp

file:
  dir: D:\work\tmp_file
  monthly-settlement-dir: D:\work\
  inter-bank-fees-dir: D:\work\tmp_file\
  import-o32-dir: D:\work\tmp_file\
  insurance-registration-fees-dir: D:\work\tmp_file\
  br-export-log-file-path: /opt/al/logpath/brexport
  ccr-rpa-file-path: /opt/al/logpath/ccrrpa
  ccr-export-file-path: /opt/al/logpath/ccrexport
  ms-export-file-path: /opt/al/logpath/msexport
  ms-import-file-path: /opt/al/logpath/msimport
  ms-rpa-file-path: /opt/al/logpath/msrpa
  br-rpa-file-path: /opt/al/logpath/brrpa
  fyb-download-file-path: /opt/al/logpath/fybdownload
  fyb-import-file-path: /opt/al/logpath/fybimport
  fyb-generate-file-path: /opt/al/logpath/fybgenerate
  br-import-sq-file-path: D:\work\tmp_file
  br-import-zz-file-path: D:\work\tmp_file
  br-upload-file-path: /opt/al/logpath/brupload
  fyi-import-file-path: /opt/al/logpath/fyiimport
  fyi-download-file-path: /opt/al/logpath/fyidownload
  fyi-generate-file-path: /opt/al/logpath/fyigenerate
  fyb-rpa-file-path: /opt/al/logpath/fybrpa


system:
  is-test: false
  open-auto-pick: true

bank-reconciliation:
  use-new-code: false

inter-bank-fees:
  flow-ids: 24,25

monthly-settlement:
  other-contacts-id: 2
  order-flow:
    config:
      1: 9
      2: 11
      3: 14
      5: 10
      6: 17
      7: 13
      9: 4
      10: 5
      11: 12
      12: 15
      13: 18


receive-email:
  host: imap.partner.outlook.cn
  password: Goodluck12345678
  defaultEncoding: utf-8
  port: 993
  protocol: imap
  debug: true
  user: <EMAIL>
  prefix-url: https://login.partner.microsoftonline.cn/
  tenant-id: ca67ccd6-8ea1-4a74-8701-a0d6f57a680c
  client-id: e0ec3d9d-5ca5-404a-adeb-ad68f3ceece9
  client-secret: **********************************
  init-max-number: 32710


sa-token:
  jwt-secret-key: sightwise8888
  active-timeout: 86400
  token-header-name: user-token
  token-style: uuid
  is-concurrent: true
  is-share: true

aes:
  key: H9V77fg7PUrxAGV1BpL8Kg==
  algorithm: AES
  transformation: AES/CBC/PKCS5Padding
  iv: abcdefghijklmnop

login:
  lock-count: 5
  # 秒为单位
  lock-time: 60
  ldap:
    url: ldap://**********:389
  ou: aziamc.com
ocr:
  secretId: AKID54qfPzOchaGi1CDKORJE4lIdsvqa8Ynp
  secretKey: ********************************
  endpoint: ocr.tencentcloudapi.com1
  base-url: http://**********:7777/api/cloud
  interface-uri: /tencent/ocr/smart-structural-pro
  token: 58da0fc6-4c77-1a3c-7f226b2f8eb47680
  use-api: true
open:
  fund:
    confirmation:
      path: /Users/<USER>/dev/temp/mail/confirmation
    reconciliation:
      path: /Users/<USER>/dev/temp/mail/reconciliation
cors:
  allowed-origins: http://***********:10006,http://*************:10006,http://localhost:9999
